#!/usr/bin/env python3
"""
测试kb_chat日志功能的脚本
"""
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'chatchat'))

def test_import():
    """测试导入是否正常"""
    try:
        from chatchat.server.chat.kb_chat import kb_chat
        print("✅ kb_chat 导入成功")
        return True
    except Exception as e:
        print(f"❌ kb_chat 导入失败: {e}")
        return False

def test_logger():
    """测试日志功能"""
    try:
        from chatchat.utils import build_logger
        logger = build_logger("test")
        logger.info("测试日志功能")
        print("✅ 日志功能正常")
        return True
    except Exception as e:
        print(f"❌ 日志功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试kb_chat日志功能...")
    print("-" * 50)
    
    # 测试导入
    if not test_import():
        sys.exit(1)
    
    # 测试日志
    if not test_logger():
        sys.exit(1)
    
    print("-" * 50)
    print("✅ 所有测试通过！")
    print("\n现在你可以启动服务器，当调用kb_chat接口时，")
    print("会在控制台和日志文件中看到发送给大模型的最终内容。")
