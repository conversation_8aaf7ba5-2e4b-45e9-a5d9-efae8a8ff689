import json
import sqlite3
import time
from typing import Dict, Optional, Any
from sqlalchemy.exc import IntegrityError
from chatchat.server.db.models.product_choices import ProductChoicesModel
from chatchat.server.db.models.product_info import ProductInfoModel
from chatchat.server.db.models.shop_info import ShopInfoModel
from chatchat.utils import build_logger
from contextlib import contextmanager
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

logger = build_logger()
local_db = 'platform_management.db'

_engine = create_engine(
    "sqlite:///platform_management.db",
    json_serializer=lambda obj: json.dumps(obj, ensure_ascii=False),
    pool_size=10,
    max_overflow=20,
    pool_timeout=30,
    pool_recycle=3600,
    pool_pre_ping=True
)
_Session = sessionmaker(bind=_engine)


@contextmanager
def session_scope():
    """自动管理 Session 的生命周期"""
    session = _Session()
    try:
        yield session
        session.commit()
    except:
        session.rollback()
        raise
    finally:
        session.close()


def create_connection(db=None, debug=None) -> Optional[sqlite3.Connection]:
    """创建数据库连接
        默认local_db连接
    Returns:
        sqlite3.Connection对象或None(如果连接失败)
    """
    if db is None:
        db = local_db
    try:
        conn = sqlite3.connect(db)
        conn.row_factory = sqlite3.Row  # 设置为Row工厂，这样查询结果可以按列名访问

        # 打印日志
        if debug is True:
            conn.set_trace_callback(printsql)

        return conn
    except sqlite3.Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None


# -------系统使用-------

def get_platform_model_by_system() -> Dict[str, Dict]:
    """
    获取所有平台及其模型信息

    Returns:
        如果查询失败返回空字典
    """

    # 使用JOIN一次性查询所有数据
    query = """
    SELECT 
        pi.id as platform_id,
        pi.business_id,
        pi.platform_name,
        pi.platform_type,
        pi.api_base_url,
        pi.api_key,
        pi.api_proxy,
        pi.api_concurrencies,
        pm.model_type,
        pm.model_name,
        pm.model_code
    FROM platform_info pi
    LEFT JOIN platform_model pm ON pi.id = pm.platform_id
    WHERE api_key != '' and api_base_url != '' and pm.is_set = 1
    ORDER BY pi.id
    """
    conn = None
    model_types = [
        'llm_models', 'embed_models', 'text2image_models',
        'image2text_models', 'rerank_models',
        'speech2text_models', 'text2speech_models'
    ]
    db_info_list = {}
    current_platform_id = None
    platform_dict = None

    try:
        conn = create_connection()
        if conn is None:
            return {}
        with conn:
            cursor = conn.execute(query)
            result = cursor.fetchall()
            if not result:
                return {}
            for row in result:
                # 当遇到新平台时，初始化平台信息
                if row['platform_id'] != current_platform_id:
                    if platform_dict is not None:
                        db_info_list[platform_dict['platform_name']] = platform_dict

                    current_platform_id = row['platform_id']
                    platform_dict = {
                        'business_id': row['business_id'],
                        'platform_name': row['platform_name'],
                        'platform_type': row['platform_type'],
                        'api_base_url': row['api_base_url'],
                        'api_key': row['api_key'],
                        'api_proxy': row['api_proxy'],
                        'api_concurrencies': row['api_concurrencies'],
                    }
                    # 初始化模型列表
                    for field in model_types:
                        platform_dict[field] = []

                # 添加模型信息（如果有）
                if row['model_type'] and row['model_type'] in platform_dict:
                    platform_dict[row['model_type']].append(row['model_code'])

            # 添加最后一个平台
            if platform_dict is not None:
                db_info_list[platform_dict['platform_name']] = platform_dict

        return db_info_list

    except sqlite3.Error as e:
        logger.error(f"查询失败: {e}")
        return {}
    finally:
        if conn:
            conn.close()


def get_default_model() -> list[str] | None:
    """
    启动时检查数据库模型配置，如果已有api key，则选择作为默认llm和emb

    Returns:
        返回对应平台的默认模型列表，如果没有有效平台则返回None
    """
    query = """
        SELECT model_type,model_code
        FROM platform_info pi
		INNER JOIN platform_model pm on pi.id = pm.platform_id
        WHERE api_key != '' AND api_key IS NOT NULL	
        AND api_base_url != '' AND api_base_url IS NOT NULL  and is_set = 1
        GROUP by model_type   ORDER BY pm.update_time desc
    """

    conn = None

    try:
        conn = create_connection()
        if conn is None:
            return None

        with conn:
            cursor = conn.execute(query)
            result = cursor.fetchall()
            # 用于初始化没有配置api key占用
            val = ['qwen3-32b', 'text-embedding-v4']
            if result:
                for v in result:
                    if v['model_type'] == 'llm_models':
                        val[0] = v['model_code']
                    if v['model_type'] == 'embed_models':
                        val[1] = v['model_code']
            # 如果是初始化,此时没有数据,返回其中一个
            return val

    except sqlite3.Error as e:
        logger.error(f"查询默认模型失败: {e}")
        return None
    finally:
        if conn:
            conn.close()


def get_platform_prompt_type() -> list:
    """
    获取模板的类型
    """
    query = "SELECT prompt_type FROM prompt_format where (is_delete is null or is_delete = 0) group by prompt_type   ORDER BY update_time desc"
    conn = None
    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return []
        with conn:
            cursor = conn.execute(query)
            result = cursor.fetchall()
            if not result:
                return []
            return [v['prompt_type'] for v in result]

    except sqlite3.Error as e:
        msg = f"数据库查询失败: {e}"
        logger.error(msg)
        return []
    finally:
        if conn:
            conn.close()


def get_platform_prompt_name(prompt_type: str) -> list:
    """
    获取指定类型的模板
    """
    if prompt_type is None:
        return []

    query = "SELECT prompt_name FROM prompt_format where (is_delete is null or is_delete = 0) and prompt_type = ?  ORDER BY update_time desc"
    conn = None
    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return []
        with conn:
            cursor = conn.execute(query, [prompt_type])
            result = cursor.fetchall()
            if not result:
                return []
            return [v['prompt_name'] for v in result]

    except sqlite3.Error as e:
        msg = f"数据库查询失败: {e}"
        logger.error(msg)
        return []
    finally:
        if conn:
            conn.close()


def get_prompt_template(prompt_type: str, prompt_name: str) -> str:
    """
    获取模板的

    Args:
        prompt_type: 提示模板类型
        prompt_name: 提示模板名字
    """
    conn = None

    query = "SELECT template FROM prompt_format WHERE (is_delete is null or is_delete = 0)  AND prompt_type = ? AND prompt_name = ?"
    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return ''

        with conn:
            cursor = conn.execute(query, (prompt_type, prompt_name))
            result = cursor.fetchone()
            if result:
                return result['template']
            return ''
    except sqlite3.Error as e:
        msg = f"数据库查询失败: {e}"
        logger.error(msg)
        return ''
    finally:
        if conn:
            conn.close()


def printsql(sql):
    print(f"执行的SQL: {sql}")


def add_chat_history(add_type: str, query: str, mode: str, knowledge_base: str, history: str, model: str,
                     prompt_name: str, answer: str, source_documents: str, view_id: str):
    # todo 后续改为 platform_shop_id 替代 view_id
    """
    写入聊天记录
    Args:
        add_type =[system 提问时自动插入,user 用户手动插入]
        chat_history 字段
    """
    if not all([add_type, query, knowledge_base]):
        return '数据为空'

    commit = """
    INSERT INTO "main"."chat_history" 
    ("query", "mode", "knowledge_base", "history", "model", "prompt_name", "answer","source_documents","add_type","view_id", "add_time", "update_time") 
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
    """
    conn = None
    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return '无法创建数据库连接'

        with conn:
            now = int(time.time())
            conn.execute(commit, (query, mode, knowledge_base, history, model, prompt_name, answer,
                                  source_documents, add_type, view_id, str(now), str(now)))
            return None

    except sqlite3.Error as e:
        msg = f"添加聊天记录失败: {e}"
        logger.error(msg)
        return None
    finally:
        if conn:
            conn.close()


# -------api使用-------

def set_platform_api_key(api_key: str, platform_id: int, platform_type: str) -> str | None:
    """
    更新指定平台的API密钥

    Args:
        api_key: 要设置的API密钥
        platform_id: 平台ID
        platform_type: 平台类型

    Returns:
        空字符串表示成功，否则返回错误信息
    """
    # 检查平台是否存在
    check_query = "SELECT 1 FROM platform_info WHERE id = ? AND platform_type = ? LIMIT 1"
    update_query = "UPDATE platform_info SET api_key = ?,update_time = ? WHERE id = ? AND platform_type = ?"
    conn = None

    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return "数据库连接失败"

        with conn:
            # 检查平台是否存在
            cursor = conn.execute(check_query, (platform_id, platform_type))
            if not cursor.fetchone():
                logger.warning(f"平台不存在: ID={platform_id}, 类型={platform_type}")
                return "指定的平台不存在"

            conn.execute(update_query, (api_key, int(time.time()), platform_id, platform_type))
            conn.commit()
            return None

    except sqlite3.Error as e:
        logger.error(f"更新平台API密钥失败: {e}")
        return f"数据库错误: {str(e)}"
    finally:
        if conn:
            conn.close()


def get_platform_info() -> str | list[dict]:
    query = "SELECT * FROM platform_info"
    conn = None

    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return "数据库连接失败"

        with conn:
            # 检查平台是否存在
            cursor = conn.execute(query)
            res = cursor.fetchall()
            if not res:
                return []
            return [dict(v) for v in res]

    except sqlite3.Error as e:
        logger.error(f"更新平台API密钥失败: {e}")
        return f"数据库错误: {str(e)}"
    finally:
        if conn:
            conn.close()


def get_platform_model(platform_type: str, model_type: str, is_show_all: bool) -> list[dict] | str:
    """
    获取大模型平台的模型
    is_show_all: 是否获取全部
    """

    query = """
        SELECT 
        pi.platform_type,pi.platform_name,pm.*
        FROM platform_model pm
        LEFT JOIN platform_info pi  ON pi.id = pm.platform_id
        WHERE api_key != '' and api_base_url != ''
    """
    param = []
    if platform_type != '':
        param.append(platform_type)
        query += " and platform_type = ?"

    if model_type != '':
        param.append(model_type)
        query += " and model_type = ?"

    # 获取全部/仅开启的
    if not is_show_all:
        query += ' and pm.is_set = 1'

    query += " ORDER BY is_set desc,model_type desc,pm.platform_id asc "
    conn = None

    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return "数据库连接失败"

        with conn:
            cursor = conn.execute(query, param)
            res = cursor.fetchall()
            if res:
                return [dict(v) for v in res]
            else:
                return "当前没有可用的模型,请去管理员-系统配置 页面添加api key"

    except sqlite3.Error as e:
        logger.error(f"更新平台API密钥失败: {e}")
        return f"数据库错误: {str(e)}"
    finally:
        if conn:
            conn.close()


def edit_platform_model(model_id: int, is_set: int) -> str | None:
    """
    开启/关闭模型
    Returns:
        空字符串表示成功，否则返回错误信息
    """
    # 参数校验
    if model_id <= 0:
        return '数据参数不全'

    update_sql = """
        UPDATE platform_model SET is_set = ?,update_time=? WHERE id = ?
    """
    conn = None

    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return '无法创建数据库连接'

        with conn:
            now = int(time.time())
            # 插入新记录
            conn.execute(update_sql, (is_set, now, model_id))
            conn.commit()  # 确保变更被提交
            return None

    except sqlite3.Error as e:
        msg = f"操作提示模板失败: {e}"
        logger.error(msg)
        return msg

    finally:
        if conn:
            conn.close()


def add_platform_model(platform_id: int, model_type: str, model_name: str, model_code: str, description: str,
                       is_set: int) -> str | None:
    """
    添加模型
    Returns:
        空字符串表示成功，否则返回错误信息
    """
    # 参数校验
    if not all([model_type, model_name, model_code]):
        return '数据参数不全'

    update_sql = """
            INSERT INTO platform_model (platform_id, model_type, model_name, model_code, description, add_time, update_time, is_set) 
            VALUES (?,?,?,?,?,?,?,?);
    """
    conn = None

    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return '无法创建数据库连接'

        with conn:
            now = int(time.time())
            # 插入新记录
            conn.execute(update_sql, (platform_id, model_type, model_name, model_code, description, now, now, is_set))
            conn.commit()  # 确保变更被提交
            return None

    except sqlite3.Error as e:
        msg = f"操作提示模板失败: {e}"
        logger.error(msg)
        return msg

    finally:
        if conn:
            conn.close()


def get_prompt_template_list(sort_direction: str, sort_column: str, prompt_type: str, prompt_name: str, page: int = 1,
                             per_page: int = 20) -> dict | str:
    """
    从数据库中获取指定类型和名称的提示模板

    Args:
        sort_direction: 排序方向
        sort_column: 排序字段
        prompt_type: 提示模板类型
        prompt_name: 提示模板名称

    Returns:
        返回找到的提示模板字符串，如果找不到或出错则返回空字符串
    """

    sort_column = sort_column if sort_column != '' else 'add_time'
    sort_direction = sort_direction if sort_direction != '' else 'desc'
    query = "SELECT * FROM prompt_format WHERE (is_delete is null or is_delete = 0)"

    data_length = """
    select count(*)c from prompt_format  WHERE (is_delete is null or is_delete = 0) 
    """
    params = []

    if prompt_type != '':
        query += " AND prompt_type = ?"
        data_length += " AND prompt_type = ?"
        params.append(prompt_type)

    if prompt_name != '':
        query += " AND prompt_name = ?"
        data_length += " AND prompt_name = ?"
        params.append(prompt_name)
    conn = None

    query += f" order by {sort_column} {sort_direction} LIMIT ? OFFSET ? "

    try:
        conn = create_connection()
        res = conn.execute(data_length, params).fetchone()
        length = res['c']
        if conn is None:
            logger.error("无法创建数据库连接")
            return '无法创建数据库连接'

        if length == 0:
            return {'total': length, 'prompt_list': []}

        with conn:
            offset = (page - 1) * per_page
            params.append(per_page)
            params.append(offset)
            cursor = conn.execute(query, params)
            result = cursor.fetchall()
            if not result:
                return {'total': length, 'prompt_list': []}
            return {'total': length, 'prompt_list': [dict(v) for v in result]}

    except sqlite3.Error as e:
        msg = f"数据库查询失败: {e}"
        logger.error(msg)
        return msg
    finally:
        if conn:
            conn.close()


def del_prompt_template(prompt_list_id: list[int]) -> str | None:
    """
    删除服务器提示模板

    Args:
        prompt_list_id: 提示模板id

    Returns:
        空字符串表示成功，否则返回错误信息
    """
    # SQL查询
    check_query = "SELECT id FROM prompt_format WHERE (is_delete is null or is_delete = 0) and id in ({}) ".format(
        ','.join(['?'] * len(prompt_list_id)))
    update_query = """
        UPDATE prompt_format SET is_delete = 1 , update_time = ?  
        WHERE (is_delete is null or is_delete = 0) and id  in({}) 
    """.format(','.join(['?'] * len(prompt_list_id)))
    conn = None

    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return '无法创建数据库连接'

        with conn:
            now = int(time.time())
            cursor = conn.execute(check_query, prompt_list_id)
            result = cursor.fetchone()

            if result:
                prompt_list_id.insert(0, now)
                # 更新现有记录
                conn.execute(update_query, prompt_list_id)
                return None
            return "数据不存在"
    except sqlite3.Error as e:
        msg = f"操作提示模板失败: {e}"
        logger.error(msg)
        return msg

    finally:
        if conn:
            conn.close()


def edit_prompt_template(prompt_id: int, prompt_type: str, prompt_name: str, template: str,
                         description: str = "") -> str | None:
    """
    设置或更新服务器提示模板

    Args:
        prompt_id: 提示模板id
        prompt_type: 提示模板类型
        prompt_name: 提示模板名称
        template: 提示模板内容
        description: 模板描述，默认为空字符串

    Returns:
        空字符串表示成功，否则返回错误信息
    """
    # 参数校验
    if not all([prompt_type, prompt_name, template]):
        return '数据参数不全'

    # SQL查询
    check_query = "SELECT id FROM prompt_format WHERE (is_delete is null or is_delete = 0) and id = ? LIMIT 1"
    update_query = """
        UPDATE prompt_format 
        SET prompt_name = ?, template = ?, description = ?, update_time = ? 
        WHERE (is_delete is null or is_delete = 0) and id = ?
    """

    conn = None

    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return '无法创建数据库连接'

        with conn:
            now = int(time.time())
            cursor = conn.execute(check_query, [str(prompt_id)])
            result = cursor.fetchone()

            if not result:
                return '数据不存在'

            # 更新现有记录
            conn.execute(update_query, (prompt_name, template, description, now, result['id']))
            conn.commit()  # 确保变更被提交
            return None

    except sqlite3.Error as e:
        msg = f"操作提示模板失败: {e}"
        logger.error(msg)
        return msg

    finally:
        if conn:
            conn.close()


def add_prompt_template(prompt_type: str, prompt_name: str, template: str,
                        description: str = "") -> str | None:
    """
    设置或更新服务器提示模板

    Args:
        prompt_id: 提示模板id
        prompt_type: 提示模板类型
        prompt_name: 提示模板名称
        template: 提示模板内容
        description: 模板描述，默认为空字符串

    Returns:
        空字符串表示成功，否则返回错误信息
    """
    # 参数校验
    if not all([prompt_type, prompt_name, template]):
        return '数据参数不全'

    insert_query = """
        INSERT INTO prompt_format 
        (prompt_type, prompt_name, template, description, add_time, update_time) 
        VALUES (?, ?, ?, ?, ?, ?)
    """
    conn = None

    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return '无法创建数据库连接'

        with conn:
            now = int(time.time())
            # 插入新记录
            conn.execute(insert_query, (prompt_type, prompt_name, template, description, now, now))
            conn.commit()  # 确保变更被提交
            return None

    except sqlite3.Error as e:
        msg = f"操作提示模板失败: {e}"
        logger.error(msg)
        return msg

    finally:
        if conn:
            conn.close()


def get_business_info(user_name: str) -> dict | str:
    """
    获取商户信息
    Args:
        user_name: 商户账号
    """

    query = "select * from business_info where is_delete = 0 and user_name = ?"
    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return "无法创建数据库连接"

        with conn:
            cursor = conn.execute(query, [user_name])
            result = cursor.fetchone()
            if result:
                return dict(result)
            return '当前商户信息不存在'
    except sqlite3.Error as e:
        logger.error(f"获取商户信息: {e}")
        return '获取商户信息'
    finally:
        if conn:
            conn.close()


def get_business_shop(user_name: str, shop_name: str) -> list[dict] | str:
    """
    获取商户信息
    Args:
        user_name: 商户账号
        shop_name: 店铺名称
    """
    query = """
    SELECT * FROM "business_info" business  INNER JOIN shop_info shop on shop.business_id = business.id
     where user_name = ?  and shop.is_delete = 0 and business.is_delete = 0 
    """
    params = [user_name]
    if shop_name:
        query += " and shop_name = ?"
        params.append(shop_name)

    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return "无法创建数据库连接"

        with conn:
            cursor = conn.execute(query, params)
            result = cursor.fetchall()
            if result:
                return [dict(row) for row in result]
            return {}
    except sqlite3.Error as e:
        logger.error(f"获取商户信息: {e}")
        return '获取商户信息'
    finally:
        if conn:
            conn.close()


def add_business_info(user_name: str, business_name: str) -> str | None:
    """
       新增商户信息
       Args:
           user_name: 商户账号
           business_name: 商户昵称
       Returns:
           成功返回操作信息，失败返回错误信息
       """
    insert_query = """
         INSERT INTO business_info
         (business_name, user_name,register_time, add_time, update_time) 
         VALUES ( ?, ?, ?, ?,?)
     """

    conn = None
    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return '无法创建数据库连接'

        with conn:
            # 插入新记录
            now = int(time.time())
            conn.execute(insert_query, [business_name, user_name, now, now, now])
            conn.commit()
            return None

    except sqlite3.Error as e:
        msg = f"操作商店知识库关系失败: {e}"
        logger.error(msg)
        return msg
    finally:
        if conn:
            conn.close()


def edit_business_info(business_id: int, business_name: str, is_delete: int) -> str | None:
    """
       编辑商店信息

       Args:
           business_id: 商户id
           business_name: 商户昵称
           is_delete: 账号状态
       Returns:
           成功返回操作信息，失败返回错误信息
       """
    insert_query = "update business_info set update_time = ?"
    params = []
    if business_name:
        insert_query += ",business_name = ? "
        params.append(business_name)

    if is_delete:
        insert_query += ",is_delete = ? "
        params.append(is_delete)

    if len(params) == 0:
        return "没有可操作的信息"

    insert_query += " where id = ? "
    params.append(business_id)
    conn = None
    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return '无法创建数据库连接'

        with conn:
            # 插入新记录
            now = int(time.time())
            params.insert(0, now)
            conn.execute(insert_query, params)
            conn.commit()
            return None

    except sqlite3.Error as e:
        msg = f"操作商店知识库关系失败: {e}"
        logger.error(msg)
        return msg
    finally:
        if conn:
            conn.close()


def add_shop(business_id: int, platform: str, view_id: str, platform_shop_id: str, shop_name: str, knowledge_base: str,
             prompt_name: str, description: str) -> str | None:
    """
       编辑商店信息

       Args:
         business_id: 商户ID
         platform: 平台
         view_id: 店铺生成唯一ID
         shop_name: 商店名称
         knowledge_base: 知识库名称
         prompt_name: prompt
         description: 关系描述，默认为空

       Returns:
           成功返回操作信息，失败返回错误信息
       """

    try:
        now = int(time.time())
        with session_scope() as session:
            is_exist = (
                session.query(ShopInfoModel)
                .filter(ShopInfoModel.platform == platform,
                        ShopInfoModel.platform_shop_id == platform_shop_id,
                        ShopInfoModel.shop_name == shop_name)
                .count()
            )
            # 如果存在则返回
            if is_exist:
                return None

            model = ShopInfoModel(business_id=business_id, platform=platform, view_id=view_id, platform_shop_id=platform_shop_id,
                                  shop_name=shop_name,
                                  knowledge_base=knowledge_base,
                                  prompt_name=prompt_name, description=description, update_time=now, add_time=now)
            session.add(model)
            session.commit()
        return None
    except IntegrityError:  # 捕获唯一约束违反异常
        session.rollback()
        return '已存在'
    except sqlite3.Error as e:
        msg = f"操作商店知识库关系失败: {e}"
        logger.error(msg)
        return msg


def get_shop_knowledge(sort_direction: str, sort_column: str, page: int, per_page: int, shop_name: str,
                       knowledge_base: str) -> dict | str:
    """
    获取商店与知识库的关联关系

    Args:
        sort_direction: 排序方向
        sort_column: 排序字段
        shop_name: 商店名称
        knowledge_base: 知识库名称

    Returns:
        JSON格式的商店知识库关系列表，出错时返回空列表JSON
    """
    query = "SELECT * FROM shop_info where 1=1 and (is_delete is null or is_delete = 0)  "
    data_length = """
    select count(*)c from shop_info  WHERE (is_delete is null or is_delete = 0) 
    """
    # 动态参数
    params = []
    if shop_name != '':
        query += " AND shop_name = ?"
        data_length += " AND shop_name = ?"
        params.append(shop_name)

    if knowledge_base != '':
        query += " AND knowledge_base = ?"
        data_length += " AND knowledge_base = ?"
        params.append(knowledge_base)

    conn = None
    query += f"order by {sort_column} {sort_direction} LIMIT ? OFFSET ?"
    offset = (page - 1) * per_page
    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return "无法创建数据库连接"

        with conn:
            res = conn.execute(data_length, params).fetchone()
            length = res['c']
            if length == 0:
                return {'total': length, "shop_knowledge": []}
            params.append(per_page)
            params.append(offset)
            cursor = conn.execute(query, params)
            result = cursor.fetchall()
            if not result:
                return {}
            return {'total': length, "shop_knowledge": [dict(row) for row in result]}
    except sqlite3.Error as e:
        logger.error(f"查询商店知识库关系失败: {e}")
        return '查询商店知识库关系失败'
    finally:
        if conn:
            conn.close()


def add_shop_knowledge(view_id: str, shop_name: str, knowledge_base: str, prompt_name: str,
                       description: str = "") -> str | None:
    # todo 后续改为 platform_shop_id 替代 view_id
    """
       新增商店与知识库的关联关系

       Args:
           view_id: 业务ID
           shop_name: 商店名称
           knowledge_base: 知识库名称
           prompt_name: prompt
           description: 关系描述，默认为空

       Returns:
           成功返回操作信息，失败返回错误信息
       """
    insert_query = """
         INSERT INTO shop_info
         (business_id, view_id,shop_name, knowledge_base, prompt_name, description, add_time, update_time) 
         VALUES ('未知',?, ?, ?, ?, ?, ?,?)
     """

    conn = None
    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return '无法创建数据库连接'

        with conn:
            # 插入新记录
            now = int(time.time())
            conn.execute(insert_query, (view_id, shop_name, knowledge_base, prompt_name, description, now, now))
            conn.commit()
            return None

    except sqlite3.Error as e:
        msg = f"操作商店知识库关系失败: {e}"
        logger.error(msg)
        return msg
    finally:
        if conn:
            conn.close()


def edit_shop_knowledge(view_id: str, shop_knowledge_id: int, shop_name: str, knowledge_base: str,
                        prompt_name: str, description: str = "") -> str | None:
    # todo 后续改为 platform_shop_id 替代 view_id
    """
    设置或更新商店与知识库的关联关系

    Args:
        view_id: 店铺生成唯一ID
        shop_knowledge_id: 店铺表ID
        shop_name: 商店名称
        knowledge_base: 知识库名称
        prompt_name: prompt
        description: 关系描述，默认为空

    Returns:
        成功返回操作信息，失败返回错误信息
    """
    if not all([shop_name, knowledge_base]):
        logger.warning("设置商店知识库关系失败: 缺少必要参数")
        return '数据参数不全'

    check_query = """SELECT id FROM shop_info """

    key_id = []
    if view_id != "":
        check_query += " WHERE view_id = ? and (is_delete is null or is_delete = 0)  LIMIT 1"
        key_id.append(view_id)
    else:
        check_query += " WHERE id = ? and (is_delete is null or is_delete = 0)  LIMIT 1"
        key_id.append(str(shop_knowledge_id))

    update_query = """
        UPDATE shop_info
        SET shop_name = ?,knowledge_base = ?, prompt_name = ? , description = ?, update_time = ? 
        WHERE id = ?  and (is_delete is null or is_delete = 0) 
    """

    conn = None
    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return '无法创建数据库连接'

        with conn:
            now = int(time.time())
            cursor = conn.execute(check_query, key_id)
            res = cursor.fetchone()
            # 如果不存在
            if not res or res['id'] == 0:
                return '当前数据不存在'

            # 更新现有记录
            conn.execute(update_query,
                         (shop_name, knowledge_base, prompt_name, description, now, res['id']))
            conn.commit()
            return None
    except sqlite3.Error as e:
        msg = f"操作商店知识库关系失败: {e}"
        logger.error(msg)
        return msg
    finally:
        if conn:
            conn.close()


def del_shop_knowledge(shop_list_id: list[int]) -> str | None:
    """
    删除服务器提示模板

    Args:
        shop_list_id: 提示模板id

    Returns:
        空字符串表示成功，否则返回错误信息
    """
    # SQL查询
    check_query = "SELECT id FROM shop_info WHERE (is_delete is null or is_delete = 0) and id in ({}) ".format(
        ','.join(['?'] * len(shop_list_id)))
    update_query = """
        UPDATE shop_infoSET is_delete = 1 , update_time = ?  
        WHERE (is_delete is null or is_delete = 0) and id  in({}) 
    """.format(','.join(['?'] * len(shop_list_id)))
    conn = None

    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return '无法创建数据库连接'

        with conn:
            now = int(time.time())
            cursor = conn.execute(check_query, shop_list_id)
            result = cursor.fetchone()

            if result:
                shop_list_id.insert(0, now)
                # 更新现有记录
                conn.execute(update_query, shop_list_id)
                return None
            return "数据不存在"
    except sqlite3.Error as e:
        msg = f"操作提示模板失败: {e}"
        logger.error(msg)
        return msg

    finally:
        if conn:
            conn.close()


def get_chat_history(sort_direction: str, sort_column: str, page: int = 1, per_page: int = 20) -> dict | str:
    # todo 后续改为 platform_shop_id 替代 view_id
    """
    获取分页聊天记录

    Args:
        sort_direction: 排序方向
        sort_column: 排序字段
        page: 页码 (从1开始)
        per_page: 每页数量

    Returns:
        List[Dict]: 聊天记录列表
        str: 错误信息
    """
    sort_column = sort_column if sort_column != '' else 'add_time'
    sort_direction = sort_direction if sort_direction != '' else 'desc'
    sql = f"""
    SELECT chat.*,shop.shop_name FROM chat_history chat 
    left join shop_info shop on chat.view_id = shop.view_id
    order by {sort_column} {sort_direction}
    LIMIT ? OFFSET ? 
    """

    data_length = """
    select count(*)c from chat_history
    """

    conn = None
    try:
        # 返回数据总长度
        conn = create_connection()
        res = conn.execute(data_length).fetchone()
        length = res['c']

        if length == 0:
            return {'total': 0, 'chat_history': []}

        offset = (page - 1) * per_page
        cursor = conn.execute(sql, (per_page, offset))
        result = cursor.fetchall()
        if not result:
            return {}
        return {
            'total': length,
            'chat_history': [dict(row) for row in result]
        }

    except sqlite3.Error as e:
        msg = f"操作失败: {e}"
        logger.error(msg)
        return msg
    finally:
        if conn:
            conn.close()


def edit_chat_history(id: int, query: str, answer: str) -> None | str:
    """
    编辑聊天记录

    Args:
        id: id
        query: 问题
        answer: 答案

    Returns:
        str: 错误信息
    """
    check_query = """SELECT id FROM chat_history WHERE id = ? LIMIT 1"""

    update_query = """
        UPDATE chat_history 
        SET query = ?,answer = ?, update_time = ? ,is_update_knowledge = Null WHERE id = ?
    """

    conn = None
    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return '无法创建数据库连接'

        with conn:
            now = int(time.time())
            cursor = conn.execute(check_query, [str(id)])
            res = cursor.fetchone()
            # 如果不存在
            if not res or res['id'] == 0:
                return '当前数据不存在'

            # 更新现有记录
            conn.execute(update_query, (query, answer, now, res['id']))
            conn.commit()
            return None
    except sqlite3.Error as e:
        msg = f"操作商店知识库关系失败: {e}"
        logger.error(msg)
        return msg
    finally:
        if conn:
            conn.close()


def get_unknowledge_chat_history_by_id(id_list: list[int]) -> list[Dict]:
    """
    获取未导入知识库的指定ID聊天记录

    Args:
        id_list: 聊天记录ID列表

    Returns:
        List[Dict]: 聊天记录列表
    """
    if not id_list:
        return []

    sql = """
    SELECT query, answer FROM chat_history 
    WHERE id IN ({}) 
    AND (is_update_knowledge IS NULL OR is_update_knowledge='false')
    """.format(','.join(['?'] * len(id_list)))

    conn = None
    try:
        conn = create_connection()
        cursor = conn.execute(sql, id_list)
        result = cursor.fetchall()
        if not result:
            return []
        return [dict(row) for row in result]
    except sqlite3.Error as e:
        msg = f"添加聊天记录失败: {e}"
        logger.error(msg)
        return []
    finally:
        if conn:
            conn.close()


def set_knowledge_chat_history_by_id(id_list: list[int]) -> Optional[str]:
    """
    标记聊天记录已导入知识库

    Args:
        id_list: 聊天记录ID列表

    Returns:
        str: 错误信息，成功返回None
    """
    if not id_list:
        return "Empty ID list"

    sql = """
    UPDATE chat_history 
    SET is_update_knowledge = 'true' 
    WHERE id IN ({}) 
    AND (is_update_knowledge IS NULL OR is_update_knowledge='false')
    """.format(','.join(['?'] * len(id_list)))

    conn = None
    try:
        conn = create_connection()
        if conn is None:
            logger.error("无法创建数据库连接")
            return "数据库连接失败"

        conn.execute(sql, id_list)
        conn.commit()
        return None
    except sqlite3.Error as e:
        msg = f"添加聊天记录失败: {e}"
        logger.error(msg)
        return None
    finally:
        if conn:
            conn.close()


def get_default_kb(default_kb: str) -> str:
    """
    标记聊天记录已导入知识库
    Returns:
        str: 错误信息，成功返回None
    """
    #todo 迁移
    sql = """
    SELECT kb_alias FROM "knowledge_base" 
    WHERE kb_alias = ? 
    OR NOT EXISTS (SELECT 1 FROM "knowledge_base" WHERE kb_alias = ?) limit 1;
    """

    conn = None
    try:
        conn = create_connection('data/knowledge_base/info.db')
        if conn is None:
            logger.error("无法创建数据库连接")
            return "数据库连接失败"

        res = conn.execute(sql, [default_kb, default_kb]).fetchone()
        if res:
            return res['kb_alias']
        return ''
    except sqlite3.Error as e:
        msg = f"添加聊天记录失败: {e}"
        logger.error(msg)
        return None
    finally:
        if conn:
            conn.close()


def add_product(data: list[dict]) -> str | None:
    """
       添加产品
       Args:
            data
       Returns:
           成功返回操作信息，失败返回错误信息
       """
    shop_name = data[0].get('shop_name', None)
    platform = data[0].get('platform', None)
    platform_shop_id = data[0].get('shop_id', None)

    try:
        now = int(time.time())
        with session_scope() as session:
            # 删除老的
            session.query(ProductInfoModel).filter(
                ProductInfoModel.shop_name == shop_name,
                ProductInfoModel.platform == platform,
                ProductInfoModel.platform_shop_id == platform_shop_id,
            ).update({"is_delete": 1})
            for v in data:
                product_model = ProductInfoModel(
                    platform=platform,
                    shop_name=shop_name,
                    product_name=v.get('product_name', None),
                    product_id=v.get('product_id', None),
                    platform_shop_id=platform_shop_id,
                    product_type=v.get('product_type', None),
                    images=json.dumps(v.get('images', None)),
                    description=v.get('description', None),
                    share_text=v.get('share_text', None),
                    url=v.get('url', None),
                    attrs=v.get('attrs', None),
                    update_time=now,
                    add_time=now
                )

                session.add(product_model)
                session.flush()  # Get the product_id before commit
                for v2 in v.get('choices', []):
                    choices_model = ProductChoicesModel(
                        product_info_id=product_model.id,
                        product_id=product_model.product_id,
                        standard=v2.get('standard', None),
                        description=v2.get('description', None),
                        amount=v2.get('amount', None),
                        single_price=v2.get('single_price', None),
                        group_price=v2.get('group_price', None),
                        reference_price=v2.get('reference_price', None),
                        image=v2.get('image', None),
                        update_time=now,
                        add_time=now
                    )
                    session.add(choices_model)

            session.commit()
        return None
    except sqlite3.Error as e:
        msg = f"操作商店知识库关系失败: {e}"
        logger.error(msg)
        return msg
